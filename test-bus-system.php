<?php
$Title_page = 'اختبار نظام الباصات';
require_once 'config.php';
require_once 'header.php';
require_once 'navbar.php';

echo "<h1>🧪 اختبار نظام حجز الباصات</h1>";

$tests = [];
$allPassed = true;

// اختبار 1: التحقق من وجود الجداول
echo "<h2>1. اختبار وجود الجداول</h2>";
$tables = ['bus_cities', 'bus_stations', 'bus_trips', 'bus_bookings'];
foreach ($tables as $table) {
    try {
        $stmt = $db->query("SELECT COUNT(*) FROM $table");
        $count = $stmt->fetchColumn();
        echo "✅ جدول $table موجود ويحتوي على $count سجل<br>";
        $tests[] = "جدول $table: نجح";
    } catch (Exception $e) {
        echo "❌ خطأ في جدول $table: " . $e->getMessage() . "<br>";
        $tests[] = "جدول $table: فشل";
        $allPassed = false;
    }
}

// اختبار 2: التحقق من البيانات التجريبية
echo "<h2>2. اختبار البيانات التجريبية</h2>";
try {
    // التحقق من المدن
    $stmt = $db->query("SELECT * FROM bus_cities ORDER BY id");
    $cities = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($cities) >= 2) {
        echo "✅ تم العثور على " . count($cities) . " مدن:<br>";
        foreach ($cities as $city) {
            echo "&nbsp;&nbsp;&nbsp;- " . $city['name'] . " (ID: " . $city['id'] . ")<br>";
        }
        $tests[] = "بيانات المدن: نجح";
    } else {
        echo "❌ لم يتم العثور على مدن كافية<br>";
        $tests[] = "بيانات المدن: فشل";
        $allPassed = false;
    }
    
    // التحقق من المحطات
    $stmt = $db->query("SELECT s.*, c.name as city_name FROM bus_stations s JOIN bus_cities c ON s.city_id = c.id ORDER BY s.id");
    $stations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($stations) >= 2) {
        echo "✅ تم العثور على " . count($stations) . " محطة:<br>";
        foreach ($stations as $station) {
            echo "&nbsp;&nbsp;&nbsp;- " . $station['name'] . " في " . $station['city_name'] . " (ID: " . $station['id'] . ")<br>";
        }
        $tests[] = "بيانات المحطات: نجح";
    } else {
        echo "❌ لم يتم العثور على محطات كافية<br>";
        $tests[] = "بيانات المحطات: فشل";
        $allPassed = false;
    }
    
    // التحقق من الرحلات
    $stmt = $db->query("SELECT t.*, ds.name as departure_station, as_table.name as arrival_station FROM bus_trips t JOIN bus_stations ds ON t.departure_station_id = ds.id JOIN bus_stations as_table ON t.arrival_station_id = as_table.id ORDER BY t.id");
    $trips = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($trips) >= 1) {
        echo "✅ تم العثور على " . count($trips) . " رحلة:<br>";
        foreach ($trips as $trip) {
            echo "&nbsp;&nbsp;&nbsp;- " . ($trip['trip_type'] === 'to_makkah' ? 'ذهاب إلى مكة' : 'عودة من مكة') . 
                 " من " . $trip['departure_station'] . " إلى " . $trip['arrival_station'] . 
                 " (" . $trip['departure_time'] . " - " . $trip['expected_arrival_time'] . ") " .
                 "بسعر " . $trip['seat_price'] . " ريال<br>";
        }
        $tests[] = "بيانات الرحلات: نجح";
    } else {
        echo "❌ لم يتم العثور على رحلات<br>";
        $tests[] = "بيانات الرحلات: فشل";
        $allPassed = false;
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في اختبار البيانات: " . $e->getMessage() . "<br>";
    $tests[] = "البيانات التجريبية: فشل";
    $allPassed = false;
}

// اختبار 3: اختبار API
echo "<h2>3. اختبار API</h2>";

// اختبار جلب المدن
echo "<h3>3.1 اختبار جلب المدن</h3>";
$testData = [
    'action' => 'get_cities',
    'trip_type' => 'to_makkah'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/moo/bus-booking-api.php');
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($testData));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 200 && $response) {
    $data = json_decode($response, true);
    if ($data && $data['success']) {
        echo "✅ API جلب المدن يعمل بشكل صحيح - تم العثور على " . count($data['cities']) . " مدن<br>";
        $tests[] = "API جلب المدن: نجح";
    } else {
        echo "❌ API جلب المدن لا يعمل بشكل صحيح<br>";
        $tests[] = "API جلب المدن: فشل";
        $allPassed = false;
    }
} else {
    echo "❌ لا يمكن الوصول إلى API (HTTP Code: $httpCode)<br>";
    $tests[] = "API جلب المدن: فشل";
    $allPassed = false;
}

// اختبار 4: اختبار حجز تجريبي
echo "<h2>4. اختبار حجز تجريبي</h2>";
try {
    // إنشاء حجز تجريبي
    $testBooking = [
        'trip_id' => 1,
        'trip_date' => date('Y-m-d', strtotime('+1 day')),
        'customer_name' => 'عميل تجريبي',
        'customer_phone' => '0501234567',
        'seats_requested' => 2
    ];
    
    $stmt = $db->prepare("INSERT INTO bus_bookings (trip_id, trip_date, customer_name, customer_phone, seats_requested) VALUES (?, ?, ?, ?, ?)");
    $result = $stmt->execute([$testBooking['trip_id'], $testBooking['trip_date'], $testBooking['customer_name'], $testBooking['customer_phone'], $testBooking['seats_requested']]);
    
    if ($result) {
        $bookingId = $db->lastInsertId();
        echo "✅ تم إنشاء حجز تجريبي برقم: $bookingId<br>";
        $tests[] = "الحجز التجريبي: نجح";
        
        // حذف الحجز التجريبي
        $stmt = $db->prepare("DELETE FROM bus_bookings WHERE id = ?");
        $stmt->execute([$bookingId]);
        echo "✅ تم حذف الحجز التجريبي<br>";
    } else {
        echo "❌ فشل في إنشاء حجز تجريبي<br>";
        $tests[] = "الحجز التجريبي: فشل";
        $allPassed = false;
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في اختبار الحجز: " . $e->getMessage() . "<br>";
    $tests[] = "الحجز التجريبي: فشل";
    $allPassed = false;
}

// اختبار 5: اختبار الملفات المطلوبة
echo "<h2>5. اختبار وجود الملفات</h2>";
$requiredFiles = [
    'booking-form.php' => 'صفحة الحجز',
    'bus-booking-api.php' => 'API الحجز',
    'booking-confirmation.php' => 'صفحة التأكيد',
    'setup_bus_system.php' => 'ملف الإعداد'
];

foreach ($requiredFiles as $file => $description) {
    if (file_exists($file)) {
        echo "✅ $description ($file) موجود<br>";
        $tests[] = "$description: نجح";
    } else {
        echo "❌ $description ($file) غير موجود<br>";
        $tests[] = "$description: فشل";
        $allPassed = false;
    }
}

// النتيجة النهائية
echo "<h2>📊 ملخص النتائج</h2>";
echo "<div style='background: " . ($allPassed ? '#d4edda' : '#f8d7da') . "; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3 style='color: " . ($allPassed ? '#155724' : '#721c24') . ";'>";
echo $allPassed ? "🎉 جميع الاختبارات نجحت!" : "⚠️ بعض الاختبارات فشلت";
echo "</h3>";

echo "<ul>";
foreach ($tests as $test) {
    $status = strpos($test, 'نجح') !== false ? '✅' : '❌';
    echo "<li>$status $test</li>";
}
echo "</ul>";

if ($allPassed) {
    echo "<p><strong>نظام حجز الباصات جاهز للاستخدام!</strong></p>";
    echo "<p><a href='booking-form.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚌 ابدأ الحجز الآن</a></p>";
} else {
    echo "<p><strong>يرجى مراجعة الأخطاء وإصلاحها قبل استخدام النظام.</strong></p>";
}

echo "</div>";

require_once 'footer.php';
?>
