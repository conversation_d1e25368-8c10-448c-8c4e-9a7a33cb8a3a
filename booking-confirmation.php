<?php
require_once 'config.php';
require_once 'header.php';

$bookingId = $_GET['id'] ?? '';

if (!$bookingId || !is_numeric($bookingId)) {
    header('Location: booking-form.php');
    exit;
}

try {
    // جلب تفاصيل الحجز
    $sql = "SELECT b.*, 
                   t.trip_type, t.departure_time, t.expected_arrival_time, t.seat_price,
                   ds.name as departure_station, 
                   as_table.name as arrival_station,
                   dc.name as departure_city,
                   ac.name as arrival_city
            FROM bus_bookings b
            JOIN bus_trips t ON b.trip_id = t.id
            JOIN bus_stations ds ON t.departure_station_id = ds.id
            JOIN bus_stations as_table ON t.arrival_station_id = as_table.id
            JOIN bus_cities dc ON ds.city_id = dc.id
            JOIN bus_cities ac ON as_table.city_id = ac.id
            WHERE b.id = ?";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$bookingId]);
    $booking = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$booking) {
        header('Location: booking-form.php');
        exit;
    }
    
} catch (Exception $e) {
    header('Location: booking-form.php');
    exit;
}

// حساب المبلغ الإجمالي
$totalAmount = $booking['seat_price'] * $booking['seats_requested'];

// تنسيق التاريخ
$tripDate = new DateTime($booking['trip_date']);
$createdAt = new DateTime($booking['created_at']);
?>

<style>
.confirmation-container {
    max-width: 600px;
    margin: 50px auto;
    padding: 30px;
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
}

.success-icon {
    font-size: 80px;
    color: #28a745;
    margin-bottom: 20px;
}

.booking-details {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 25px;
    margin: 25px 0;
    text-align: right;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #dee2e6;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: bold;
    color: #495057;
}

.detail-value {
    color: #212529;
}

.total-amount {
    background: #e7f3ff;
    border: 2px solid #007bff;
    border-radius: 8px;
    padding: 15px;
    margin: 20px 0;
    font-size: 18px;
    font-weight: bold;
    color: #007bff;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    margin: 10px;
    transition: all 0.3s;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.trip-type-badge {
    display: inline-block;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;
}

.to-makkah {
    background: #d4edda;
    color: #155724;
}

.from-makkah {
    background: #fff3cd;
    color: #856404;
}

@media print {
    .btn {
        display: none;
    }
    
    .confirmation-container {
        box-shadow: none;
        margin: 0;
    }
}
</style>

<div class="confirmation-container">
    <div class="success-icon">✅</div>
    
    <h1 style="color: #28a745; margin-bottom: 10px;">تم تأكيد الحجز بنجاح!</h1>
    <p style="color: #6c757d; margin-bottom: 30px;">شكراً لك، تم حجز مقاعدك بنجاح</p>
    
    <div class="booking-details">
        <h3 style="text-align: center; margin-bottom: 25px; color: #333;">تفاصيل الحجز</h3>
        
        <div class="detail-row">
            <span class="detail-label">رقم الحجز:</span>
            <span class="detail-value" style="font-weight: bold; color: #007bff;">#<?php echo $booking['id']; ?></span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">نوع الرحلة:</span>
            <span class="detail-value">
                <span class="trip-type-badge <?php echo $booking['trip_type'] === 'to_makkah' ? 'to-makkah' : 'from-makkah'; ?>">
                    <?php echo $booking['trip_type'] === 'to_makkah' ? 'ذهاب إلى مكة المكرمة' : 'عودة من مكة المكرمة'; ?>
                </span>
            </span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">اسم العميل:</span>
            <span class="detail-value"><?php echo htmlspecialchars($booking['customer_name']); ?></span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">رقم الهاتف:</span>
            <span class="detail-value"><?php echo htmlspecialchars($booking['customer_phone']); ?></span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">من:</span>
            <span class="detail-value"><?php echo $booking['departure_station'] . ' - ' . $booking['departure_city']; ?></span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">إلى:</span>
            <span class="detail-value"><?php echo $booking['arrival_station'] . ' - ' . $booking['arrival_city']; ?></span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">تاريخ الرحلة:</span>
            <span class="detail-value"><?php echo $tripDate->format('Y-m-d') . ' (' . getDayName($tripDate->format('w')) . ')'; ?></span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">وقت الانطلاق:</span>
            <span class="detail-value"><?php echo date('H:i', strtotime($booking['departure_time'])); ?></span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">وقت الوصول المتوقع:</span>
            <span class="detail-value"><?php echo date('H:i', strtotime($booking['expected_arrival_time'])); ?></span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">عدد المقاعد:</span>
            <span class="detail-value"><?php echo $booking['seats_requested']; ?></span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">سعر المقعد:</span>
            <span class="detail-value"><?php echo number_format($booking['seat_price'], 2); ?> ريال</span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">تاريخ الحجز:</span>
            <span class="detail-value"><?php echo $createdAt->format('Y-m-d H:i'); ?></span>
        </div>
    </div>
    
    <div class="total-amount">
        المبلغ الإجمالي: <?php echo number_format($totalAmount, 2); ?> ريال سعودي
    </div>
    
    <div style="margin-top: 30px;">
        <button onclick="window.print()" class="btn btn-secondary">🖨️ طباعة</button>
        <a href="booking-form.php" class="btn btn-primary">حجز رحلة جديدة</a>
    </div>
    
    <div style="margin-top: 30px; padding: 20px; background: #e9ecef; border-radius: 8px; font-size: 14px; color: #6c757d;">
        <h4 style="color: #495057;">ملاحظات مهمة:</h4>
        <ul style="text-align: right; margin: 0; padding-right: 20px;">
            <li>يرجى الوصول إلى المحطة قبل موعد الانطلاق بـ 30 دقيقة على الأقل</li>
            <li>يرجى إحضار هوية شخصية سارية المفعول</li>
            <li>في حالة التأخير أو عدم الحضور، لن يتم استرداد المبلغ</li>
            <li>للاستفسارات أو التغييرات، يرجى التواصل معنا</li>
        </ul>
    </div>
</div>

<?php
function getDayName($dayNumber) {
    $days = [
        '0' => 'الأحد',
        '1' => 'الاثنين', 
        '2' => 'الثلاثاء',
        '3' => 'الأربعاء',
        '4' => 'الخميس',
        '5' => 'الجمعة',
        '6' => 'السبت'
    ];
    
    return $days[$dayNumber] ?? '';
}

require_once 'footer.php';
?>
