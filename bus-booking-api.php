<?php
header('Content-Type: application/json; charset=utf-8');
require_once 'config.php';

try {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'get_cities':
            getCities();
            break;
            
        case 'get_stations':
            getStations();
            break;
            
        case 'search_trips':
            searchTrips();
            break;
            
        case 'confirm_booking':
            confirmBooking();
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'خطأ في الخادم: ' . $e->getMessage()]);
}

function getCities() {
    global $db;
    
    $tripType = $_POST['trip_type'] ?? '';
    
    if ($tripType === 'to_makkah') {
        // للذهاب إلى مكة، نعرض جميع المدن عدا مكة
        $sql = "SELECT id, name FROM bus_cities WHERE name != 'مكة المكرمة' ORDER BY name";
    } else {
        // للعودة من مكة، نعرض مكة فقط
        $sql = "SELECT id, name FROM bus_cities WHERE name = 'مكة المكرمة' ORDER BY name";
    }
    
    $stmt = $db->query($sql);
    $cities = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode(['success' => true, 'cities' => $cities]);
}

function getStations() {
    global $db;
    
    $cityId = $_POST['city_id'] ?? '';
    $tripType = $_POST['trip_type'] ?? '';
    
    if (!$cityId || !$tripType) {
        echo json_encode(['success' => false, 'message' => 'بيانات ناقصة']);
        return;
    }
    
    $sql = "SELECT id, name FROM bus_stations WHERE city_id = ? ORDER BY name";
    $stmt = $db->prepare($sql);
    $stmt->execute([$cityId]);
    $stations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode(['success' => true, 'stations' => $stations]);
}

function searchTrips() {
    global $db;
    
    $tripType = $_POST['trip_type'] ?? '';
    $stationId = $_POST['station_id'] ?? '';
    $tripDate = $_POST['trip_date'] ?? '';
    $seatsCount = $_POST['seats_count'] ?? '';
    
    if (!$tripType || !$stationId || !$tripDate || !$seatsCount) {
        echo json_encode(['success' => false, 'message' => 'بيانات ناقصة']);
        return;
    }
    
    // التحقق من صحة التاريخ
    $selectedDate = new DateTime($tripDate);
    $today = new DateTime();
    
    if ($selectedDate < $today) {
        echo json_encode(['success' => false, 'message' => 'لا يمكن حجز رحلة في تاريخ سابق']);
        return;
    }
    
    // الحصول على رقم اليوم (0 = الأحد، 6 = السبت)
    $dayOfWeek = $selectedDate->format('w');
    
    // البحث عن الرحلات المتاحة
    if ($tripType === 'to_makkah') {
        $sql = "SELECT t.*, 
                       ds.name as departure_station, 
                       as_table.name as arrival_station,
                       dc.name as departure_city,
                       ac.name as arrival_city
                FROM bus_trips t
                JOIN bus_stations ds ON t.departure_station_id = ds.id
                JOIN bus_stations as_table ON t.arrival_station_id = as_table.id
                JOIN bus_cities dc ON ds.city_id = dc.id
                JOIN bus_cities ac ON as_table.city_id = ac.id
                WHERE t.trip_type = 'to_makkah' 
                AND t.departure_station_id = ?
                AND t.is_active = 1
                AND (t.available_days = '0' OR JSON_CONTAINS(t.available_days, ?))";
    } else {
        $sql = "SELECT t.*, 
                       ds.name as departure_station, 
                       as_table.name as arrival_station,
                       dc.name as departure_city,
                       ac.name as arrival_city
                FROM bus_trips t
                JOIN bus_stations ds ON t.departure_station_id = ds.id
                JOIN bus_stations as_table ON t.arrival_station_id = as_table.id
                JOIN bus_cities dc ON ds.city_id = dc.id
                JOIN bus_cities ac ON as_table.city_id = ac.id
                WHERE t.trip_type = 'from_makkah' 
                AND t.departure_station_id = ?
                AND t.is_active = 1
                AND (t.available_days = '0' OR JSON_CONTAINS(t.available_days, ?))";
    }
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$stationId, '"' . $dayOfWeek . '"']);
    $trips = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode(['success' => true, 'trips' => $trips]);
}

function confirmBooking() {
    global $db;
    
    $tripId = $_POST['trip_id'] ?? '';
    $tripDate = $_POST['trip_date'] ?? '';
    $seatsCount = $_POST['seats_count'] ?? '';
    $customerName = trim($_POST['customer_name'] ?? '');
    $customerPhone = trim($_POST['customer_phone'] ?? '');
    
    // التحقق من البيانات
    if (!$tripId || !$tripDate || !$seatsCount || !$customerName || !$customerPhone) {
        echo json_encode(['success' => false, 'message' => 'جميع الحقول مطلوبة']);
        return;
    }
    
    if (!is_numeric($seatsCount) || $seatsCount < 1 || $seatsCount > 10) {
        echo json_encode(['success' => false, 'message' => 'عدد المقاعد يجب أن يكون بين 1 و 10']);
        return;
    }
    
    if (strlen($customerName) < 3) {
        echo json_encode(['success' => false, 'message' => 'اسم العميل يجب أن يكون 3 أحرف على الأقل']);
        return;
    }
    
    if (!preg_match('/^[0-9+\-\s]{10,15}$/', $customerPhone)) {
        echo json_encode(['success' => false, 'message' => 'رقم الهاتف غير صحيح']);
        return;
    }
    
    // التحقق من وجود الرحلة
    $stmt = $db->prepare("SELECT * FROM bus_trips WHERE id = ? AND is_active = 1");
    $stmt->execute([$tripId]);
    $trip = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$trip) {
        echo json_encode(['success' => false, 'message' => 'الرحلة غير موجودة أو غير متاحة']);
        return;
    }
    
    // التحقق من صحة التاريخ مرة أخرى
    $selectedDate = new DateTime($tripDate);
    $today = new DateTime();
    
    if ($selectedDate < $today) {
        echo json_encode(['success' => false, 'message' => 'لا يمكن حجز رحلة في تاريخ سابق']);
        return;
    }
    
    // التحقق من توفر الرحلة في هذا اليوم
    $dayOfWeek = $selectedDate->format('w');
    $availableDays = $trip['available_days'];
    
    if ($availableDays !== '0') {
        $daysArray = json_decode($availableDays, true);
        if (!in_array($dayOfWeek, $daysArray)) {
            echo json_encode(['success' => false, 'message' => 'الرحلة غير متاحة في هذا اليوم']);
            return;
        }
    }
    
    try {
        // إدراج الحجز
        $stmt = $db->prepare("INSERT INTO bus_bookings (trip_id, trip_date, customer_name, customer_phone, seats_requested) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$tripId, $tripDate, $customerName, $customerPhone, $seatsCount]);
        
        $bookingId = $db->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم تأكيد الحجز بنجاح',
            'booking_id' => $bookingId,
            'customer_name' => $customerName,
            'seats_count' => $seatsCount,
            'trip_date' => $tripDate,
            'redirect_url' => 'booking-confirmation.php?id=' . $bookingId
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'خطأ في حفظ الحجز: ' . $e->getMessage()]);
    }
}
?>
