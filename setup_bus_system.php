<?php
require_once 'config.php';
require_once 'bus_header.php';

try {
    // إنشاء جدول المدن
    $sql = "CREATE TABLE IF NOT EXISTS `bus_cities` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(255) NOT NULL,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $db->exec($sql);
    echo "✅ تم إنشاء جدول bus_cities<br>";

    // إنشاء جدول المحطات
    $sql = "CREATE TABLE IF NOT EXISTS `bus_stations` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `city_id` int(11) NOT NULL,
        `name` varchar(255) NOT NULL,
        PRIMARY KEY (`id`),
        KEY `fk_station_city` (`city_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $db->exec($sql);
    echo "✅ تم إنشاء جدول bus_stations<br>";

    // إنشاء جدول الرحلات
    $sql = "CREATE TABLE IF NOT EXISTS `bus_trips` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `trip_type` enum('to_makkah','from_makkah') NOT NULL,
        `departure_station_id` int(11) NOT NULL,
        `arrival_station_id` int(11) NOT NULL,
        `departure_time` time NOT NULL,
        `expected_arrival_time` time NOT NULL,
        `seat_price` decimal(8,2) NOT NULL,
        `available_days` text NOT NULL COMMENT 'JSON array or \"0\" for daily',
        `is_active` tinyint(1) NOT NULL DEFAULT 1,
        PRIMARY KEY (`id`),
        KEY `fk_trip_departure_station` (`departure_station_id`),
        KEY `fk_trip_arrival_station` (`arrival_station_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $db->exec($sql);
    echo "✅ تم إنشاء جدول bus_trips<br>";

    // إنشاء جدول الحجوزات
    $sql = "CREATE TABLE IF NOT EXISTS `bus_bookings` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `trip_id` int(11) NOT NULL,
        `trip_date` date NOT NULL,
        `customer_name` varchar(255) NOT NULL,
        `customer_phone` varchar(20) NOT NULL,
        `seats_requested` int(11) NOT NULL,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        PRIMARY KEY (`id`),
        KEY `fk_booking_trip` (`trip_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $db->exec($sql);
    echo "✅ تم إنشاء جدول bus_bookings<br>";

    // إضافة القيود الخارجية
    try {
        $db->exec("ALTER TABLE `bus_stations` ADD CONSTRAINT `fk_station_city` FOREIGN KEY (`city_id`) REFERENCES `bus_cities` (`id`) ON DELETE CASCADE");
    } catch (Exception $e) {
        // القيد موجود بالفعل
    }

    try {
        $db->exec("ALTER TABLE `bus_trips` ADD CONSTRAINT `fk_trip_departure_station` FOREIGN KEY (`departure_station_id`) REFERENCES `bus_stations` (`id`) ON DELETE CASCADE");
    } catch (Exception $e) {
        // القيد موجود بالفعل
    }

    try {
        $db->exec("ALTER TABLE `bus_trips` ADD CONSTRAINT `fk_trip_arrival_station` FOREIGN KEY (`arrival_station_id`) REFERENCES `bus_stations` (`id`) ON DELETE CASCADE");
    } catch (Exception $e) {
        // القيد موجود بالفعل
    }

    try {
        $db->exec("ALTER TABLE `bus_bookings` ADD CONSTRAINT `fk_booking_trip` FOREIGN KEY (`trip_id`) REFERENCES `bus_trips` (`id`) ON DELETE CASCADE");
    } catch (Exception $e) {
        // القيد موجود بالفعل
    }

    echo "✅ تم إضافة القيود الخارجية<br>";

    // إضافة البيانات التجريبية
    // التحقق من وجود البيانات أولاً
    $stmt = $db->query("SELECT COUNT(*) FROM bus_cities");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        // إضافة المدن
        $stmt = $db->prepare("INSERT INTO `bus_cities` (`name`) VALUES (?)");
        $stmt->execute(['الرياض']);
        $riyadh_id = $db->lastInsertId();
        
        $stmt->execute(['مكة المكرمة']);
        $makkah_id = $db->lastInsertId();
        
        echo "✅ تم إضافة المدن<br>";

        // إضافة المحطات
        $stmt = $db->prepare("INSERT INTO `bus_stations` (`city_id`, `name`) VALUES (?, ?)");
        $stmt->execute([$riyadh_id, 'محطة البطحاء']);
        $riyadh_station_id = $db->lastInsertId();
        
        $stmt->execute([$makkah_id, 'شارع ابراهيم الخليل']);
        $makkah_station_id = $db->lastInsertId();
        
        echo "✅ تم إضافة المحطات<br>";

        // إضافة الرحلات
        $stmt = $db->prepare("INSERT INTO `bus_trips` (`trip_type`, `departure_station_id`, `arrival_station_id`, `departure_time`, `expected_arrival_time`, `seat_price`, `available_days`, `is_active`) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        
        // رحلة ذهاب إلى مكة
        $stmt->execute(['to_makkah', $riyadh_station_id, $makkah_station_id, '13:30:00', '19:00:00', 100.00, '0', 1]);
        
        // رحلة عودة من مكة
        $stmt->execute(['from_makkah', $makkah_station_id, $riyadh_station_id, '08:00:00', '13:30:00', 100.00, '0', 1]);
        
        echo "✅ تم إضافة الرحلات<br>";
    } else {
        echo "ℹ️ البيانات التجريبية موجودة بالفعل<br>";
    }

    echo "<br><h2>🎉 تم إعداد نظام حجز الباصات بنجاح!</h2>";
    echo "<p><a href='booking-form.php'>انتقل إلى صفحة الحجز</a></p>";

} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
}

require_once 'bus_footer.php';
?>
