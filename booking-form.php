<?php
require_once 'config.php';
require_once 'bus_header.php';
?>

<style>
.bus-booking-container {
    max-width: 800px;
    margin: 20px auto;
    padding: 30px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.form-control:focus {
    border-color: #007bff;
    outline: none;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #1e7e34;
}

.trip-card {
    border: 2px solid #ddd;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
    cursor: pointer;
    transition: all 0.3s;
}

.trip-card:hover {
    border-color: #007bff;
    box-shadow: 0 5px 15px rgba(0,123,255,0.2);
}

.trip-card.selected {
    border-color: #007bff;
    background: #f8f9ff;
}

.trip-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.trip-time {
    font-size: 18px;
    font-weight: bold;
    color: #007bff;
}

.trip-price {
    font-size: 20px;
    font-weight: bold;
    color: #28a745;
}

.hidden {
    display: none;
}

.alert {
    padding: 15px;
    margin: 20px 0;
    border-radius: 8px;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-danger {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.loading {
    text-align: center;
    padding: 20px;
}
</style>

<div class="bus-booking-container">
    <h1 style="text-align: center; color: #333; margin-bottom: 30px;">
        🚌 حجز مقاعد الباصات
    </h1>

    <form id="busBookingForm">
        <!-- نوع الرحلة -->
        <div class="form-group">
            <label for="tripType">نوع الرحلة:</label>
            <select id="tripType" name="trip_type" class="form-control" required>
                <option value="">اختر نوع الرحلة</option>
                <option value="to_makkah">ذهاب إلى مكة المكرمة</option>
                <option value="from_makkah">عودة من مكة المكرمة</option>
            </select>
        </div>

        <!-- المدينة -->
        <div class="form-group">
            <label for="cityId">المدينة:</label>
            <select id="cityId" name="city_id" class="form-control" required disabled>
                <option value="">اختر المدينة</option>
            </select>
        </div>

        <!-- المحطة -->
        <div class="form-group">
            <label for="stationId">محطة الانطلاق:</label>
            <select id="stationId" name="station_id" class="form-control" required disabled>
                <option value="">اختر المحطة</option>
            </select>
        </div>

        <!-- تاريخ الرحلة -->
        <div class="form-group">
            <label for="tripDate">تاريخ الرحلة:</label>
            <input type="date" id="tripDate" name="trip_date" class="form-control" required disabled>
        </div>

        <!-- عدد المقاعد -->
        <div class="form-group">
            <label for="seatsCount">عدد المقاعد المطلوبة:</label>
            <input type="number" id="seatsCount" name="seats_count" class="form-control" min="1" max="10" required disabled>
        </div>

        <!-- زر البحث -->
        <div class="form-group">
            <button type="button" id="searchTrips" class="btn btn-primary" disabled>
                🔍 البحث عن الرحلات المتاحة
            </button>
        </div>
    </form>

    <!-- عرض الرحلات المتاحة -->
    <div id="tripsContainer" class="hidden">
        <h3>الرحلات المتاحة:</h3>
        <div id="tripsList"></div>
    </div>

    <!-- نموذج بيانات العميل -->
    <div id="customerForm" class="hidden">
        <h3>بيانات العميل:</h3>
        <form id="bookingForm">
            <input type="hidden" id="selectedTripId" name="trip_id">
            <input type="hidden" id="selectedTripDate" name="trip_date">
            <input type="hidden" id="selectedSeatsCount" name="seats_count">
            
            <div class="form-group">
                <label for="customerName">الاسم الكامل:</label>
                <input type="text" id="customerName" name="customer_name" class="form-control" required>
            </div>
            
            <div class="form-group">
                <label for="customerPhone">رقم الهاتف:</label>
                <input type="tel" id="customerPhone" name="customer_phone" class="form-control" required>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-success">
                    ✅ تأكيد الحجز
                </button>
            </div>
        </form>
    </div>

    <!-- رسائل النظام -->
    <div id="messages"></div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    let selectedTripId = null;

    // تحميل المدن عند تغيير نوع الرحلة
    $('#tripType').change(function() {
        const tripType = $(this).val();
        if (tripType) {
            loadCities(tripType);
            $('#cityId').prop('disabled', false);
        } else {
            resetForm();
        }
    });

    // تحميل المحطات عند تغيير المدينة
    $('#cityId').change(function() {
        const cityId = $(this).val();
        const tripType = $('#tripType').val();
        if (cityId && tripType) {
            loadStations(cityId, tripType);
            $('#stationId').prop('disabled', false);
        } else {
            $('#stationId').prop('disabled', true).html('<option value="">اختر المحطة</option>');
        }
    });

    // تفعيل باقي الحقول عند اختيار المحطة
    $('#stationId').change(function() {
        if ($(this).val()) {
            $('#tripDate').prop('disabled', false);
            $('#seatsCount').prop('disabled', false);
            $('#searchTrips').prop('disabled', false);
            
            // تعيين الحد الأدنى للتاريخ (اليوم)
            const today = new Date().toISOString().split('T')[0];
            $('#tripDate').attr('min', today);
        }
    });

    // البحث عن الرحلات
    $('#searchTrips').click(function() {
        searchTrips();
    });

    // تأكيد الحجز
    $('#bookingForm').submit(function(e) {
        e.preventDefault();
        confirmBooking();
    });

    function loadCities(tripType) {
        $.ajax({
            url: 'bus-booking-api.php',
            method: 'POST',
            data: { action: 'get_cities', trip_type: tripType },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    let options = '<option value="">اختر المدينة</option>';
                    response.cities.forEach(function(city) {
                        options += `<option value="${city.id}">${city.name}</option>`;
                    });
                    $('#cityId').html(options);
                }
            }
        });
    }

    function loadStations(cityId, tripType) {
        $.ajax({
            url: 'bus-booking-api.php',
            method: 'POST',
            data: { action: 'get_stations', city_id: cityId, trip_type: tripType },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    let options = '<option value="">اختر المحطة</option>';
                    response.stations.forEach(function(station) {
                        options += `<option value="${station.id}">${station.name}</option>`;
                    });
                    $('#stationId').html(options);
                }
            }
        });
    }

    function searchTrips() {
        const formData = {
            action: 'search_trips',
            trip_type: $('#tripType').val(),
            station_id: $('#stationId').val(),
            trip_date: $('#tripDate').val(),
            seats_count: $('#seatsCount').val()
        };

        $('#tripsList').html('<div class="loading">جاري البحث...</div>');
        $('#tripsContainer').removeClass('hidden');

        $.ajax({
            url: 'bus-booking-api.php',
            method: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success && response.trips.length > 0) {
                    displayTrips(response.trips);
                } else {
                    $('#tripsList').html('<div class="alert alert-danger">لا توجد رحلات متاحة في هذا التاريخ</div>');
                }
            },
            error: function() {
                $('#tripsList').html('<div class="alert alert-danger">حدث خطأ أثناء البحث</div>');
            }
        });
    }

    function displayTrips(trips) {
        let html = '';
        trips.forEach(function(trip) {
            html += `
                <div class="trip-card" data-trip-id="${trip.id}">
                    <div class="trip-info">
                        <div>
                            <div class="trip-time">${trip.departure_time} - ${trip.expected_arrival_time}</div>
                            <div>من: ${trip.departure_station} إلى: ${trip.arrival_station}</div>
                        </div>
                        <div class="trip-price">${trip.seat_price} ريال</div>
                    </div>
                </div>
            `;
        });
        $('#tripsList').html(html);

        // إضافة حدث النقر على الرحلة
        $('.trip-card').click(function() {
            $('.trip-card').removeClass('selected');
            $(this).addClass('selected');
            selectedTripId = $(this).data('trip-id');
            
            // إظهار نموذج بيانات العميل
            $('#selectedTripId').val(selectedTripId);
            $('#selectedTripDate').val($('#tripDate').val());
            $('#selectedSeatsCount').val($('#seatsCount').val());
            $('#customerForm').removeClass('hidden');
        });
    }

    function confirmBooking() {
        const formData = $('#bookingForm').serialize() + '&action=confirm_booking';
        
        $.ajax({
            url: 'bus-booking-api.php',
            method: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // إعادة التوجيه إلى صفحة التأكيد
                    if (response.redirect_url) {
                        window.location.href = response.redirect_url;
                    } else {
                        $('#messages').html(`
                            <div class="alert alert-success">
                                <h4>تم تأكيد الحجز بنجاح! 🎉</h4>
                                <p><strong>رقم الحجز:</strong> ${response.booking_id}</p>
                                <p><strong>اسم العميل:</strong> ${response.customer_name}</p>
                                <p><strong>عدد المقاعد:</strong> ${response.seats_count}</p>
                                <p><strong>تاريخ الرحلة:</strong> ${response.trip_date}</p>
                            </div>
                        `);

                        // إعادة تعيين النموذج
                        resetForm();
                        $('#customerForm').addClass('hidden');
                        $('#tripsContainer').addClass('hidden');
                    }
                } else {
                    $('#messages').html(`<div class="alert alert-danger">خطأ: ${response.message}</div>`);
                }
            },
            error: function() {
                $('#messages').html('<div class="alert alert-danger">حدث خطأ أثناء تأكيد الحجز</div>');
            }
        });
    }

    function resetForm() {
        $('#cityId').prop('disabled', true).html('<option value="">اختر المدينة</option>');
        $('#stationId').prop('disabled', true).html('<option value="">اختر المحطة</option>');
        $('#tripDate').prop('disabled', true).val('');
        $('#seatsCount').prop('disabled', true).val('');
        $('#searchTrips').prop('disabled', true);
        $('#tripsContainer').addClass('hidden');
        $('#customerForm').addClass('hidden');
        selectedTripId = null;
    }
});
</script>

<?php require_once 'bus_footer.php'; ?>
