<?php
$Title_page = 'حجز الباصات';
require_once 'config.php';
require_once 'header.php';
require_once 'navbar.php';
?>

<style>
/* خلفية برتقالية مع تدرج */
.bus-hero-section {
    background: linear-gradient(135deg, #ff8c00 0%, #ff6b00 50%, #ff4500 100%);
    min-height: 500px;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
}

.bus-hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.bus-booking-container {
    max-width: 450px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 2;
}

.bus-image {
    position: absolute;
    right: -50px;
    bottom: -20px;
    width: 600px;
    height: auto;
    z-index: 1;
    opacity: 0.9;
}

.hero-text {
    color: white;
    font-size: 2.5rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin-bottom: 10px;
}

.hero-subtitle {
    color: rgba(255,255,255,0.9);
    font-size: 1.2rem;
    margin-bottom: 30px;
}

@media (max-width: 768px) {
    .bus-image {
        width: 400px;
        right: -100px;
    }
    .hero-text {
        font-size: 2rem;
    }
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-control:focus {
    border-color: #ff6b00;
    outline: none;
    background: white;
    box-shadow: 0 0 0 3px rgba(255, 107, 0, 0.1);
}

.form-control option {
    padding: 10px;
}

.btn {
    padding: 15px 30px;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: none;
}

.btn-primary {
    background: linear-gradient(135deg, #ff6b00 0%, #ff8c00 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 0, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #ff5500 0%, #ff7700 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 0, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, #1e7e34 0%, #17a2b8 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.trip-card {
    border: 2px solid #ddd;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
    cursor: pointer;
    transition: all 0.3s;
}

.trip-card:hover {
    border-color: #007bff;
    box-shadow: 0 5px 15px rgba(0,123,255,0.2);
}

.trip-card.selected {
    border-color: #007bff;
    background: #f8f9ff;
}

.trip-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.trip-time {
    font-size: 18px;
    font-weight: bold;
    color: #007bff;
}

.trip-price {
    font-size: 20px;
    font-weight: bold;
    color: #28a745;
}

.hidden {
    display: none;
}

.alert {
    padding: 15px;
    margin: 20px 0;
    border-radius: 8px;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-danger {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.loading {
    text-align: center;
    padding: 20px;
}
</style>

<!-- Hero Section مع خلفية برتقالية -->
<section class="bus-hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="hero-text">باصات من وإلى مكة</div>
                <div class="hero-subtitle">احجز رحلتك بسهولة وأمان</div>

                <div class="bus-booking-container">
                    <div class="d-flex align-items-center mb-4">
                        <div class="me-3">
                            <span class="badge bg-success px-3 py-2">
                                <i class="fa fa-check me-1"></i>
                                ذهاب إلى مكة
                            </span>
                        </div>
                        <div>
                            <span class="badge bg-primary px-3 py-2">
                                <i class="fa fa-map-marker me-1"></i>
                                عودة من مكة
                            </span>
                        </div>
                    </div>

                    <form id="busBookingForm">
                        <!-- نوع الرحلة -->
                        <div class="form-group">
                            <label for="tripType">
                                <i class="fa fa-route me-2"></i>نوع الرحلة
                            </label>
                            <select id="tripType" name="trip_type" class="form-control" required>
                                <option value="">اختر نوع الرحلة</option>
                                <option value="to_makkah">ذهاب إلى مكة المكرمة</option>
                                <option value="from_makkah">عودة من مكة المكرمة</option>
                            </select>
                        </div>

                        <!-- المدينة -->
                        <div class="form-group">
                            <label for="cityId">
                                <i class="fa fa-map-marker me-2"></i>المدينة
                            </label>
                            <select id="cityId" name="city_id" class="form-control" required disabled>
                                <option value="">اختر المدينة</option>
                            </select>
                        </div>

                        <!-- المحطة -->
                        <div class="form-group">
                            <label for="stationId">
                                <i class="fa fa-building me-2"></i>محطة الانطلاق
                            </label>
                            <select id="stationId" name="station_id" class="form-control" required disabled>
                                <option value="">اختر المحطة</option>
                            </select>
                        </div>

                        <!-- تاريخ الرحلة -->
                        <div class="form-group">
                            <label for="tripDate">
                                <i class="fa fa-calendar me-2"></i>تاريخ الرحلة
                            </label>
                            <input type="date" id="tripDate" name="trip_date" class="form-control" required disabled>
                        </div>

                        <!-- عدد المقاعد -->
                        <div class="form-group">
                            <label for="seatsCount">
                                <i class="fa fa-users me-2"></i>عدد المسافرين
                            </label>
                            <div class="d-flex align-items-center">
                                <button type="button" class="btn btn-outline-secondary btn-sm me-2" onclick="changeSeats(-1)">-</button>
                                <input type="number" id="seatsCount" name="seats_count" class="form-control text-center"
                                       min="1" max="10" value="1" required disabled style="max-width: 80px;">
                                <button type="button" class="btn btn-outline-secondary btn-sm ms-2" onclick="changeSeats(1)">+</button>
                            </div>
                            <small class="text-muted">ينطبق بحث خدمة الانتقال لجميع مع سنوات</small>
                        </div>

                        <!-- زر البحث -->
                        <div class="form-group">
                            <button type="button" id="searchTrips" class="btn btn-primary w-100" disabled>
                                <i class="fa fa-search me-2"></i>أعرض الرحلات
                            </button>
                        </div>
                    </form>

    <!-- عرض الرحلات المتاحة -->
    <div id="tripsContainer" class="hidden">
        <h3>الرحلات المتاحة:</h3>
        <div id="tripsList"></div>
    </div>

    <!-- نموذج بيانات العميل -->
    <div id="customerForm" class="hidden">
        <h3>بيانات العميل:</h3>
        <form id="bookingForm">
            <input type="hidden" id="selectedTripId" name="trip_id">
            <input type="hidden" id="selectedTripDate" name="trip_date">
            <input type="hidden" id="selectedSeatsCount" name="seats_count">
            
            <div class="form-group">
                <label for="customerName">الاسم الكامل:</label>
                <input type="text" id="customerName" name="customer_name" class="form-control" required>
            </div>
            
            <div class="form-group">
                <label for="customerPhone">رقم الهاتف:</label>
                <input type="tel" id="customerPhone" name="customer_phone" class="form-control" required>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-success">
                    ✅ تأكيد الحجز
                </button>
            </div>
        </form>
    </div>

                    <!-- رسائل النظام -->
                    <div id="messages"></div>
                </div>
            </div>

            <div class="col-lg-6 text-end position-relative">
                <!-- صورة الباص -->
                <img src="https://images.unsplash.com/photo-1544620347-c4fd4a3d5957?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                     alt="باص حديث" class="bus-image">
            </div>
        </div>
    </div>
</section>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
// دالة تغيير عدد المقاعد
function changeSeats(change) {
    const seatsInput = document.getElementById('seatsCount');
    let currentValue = parseInt(seatsInput.value) || 1;
    let newValue = currentValue + change;

    if (newValue >= 1 && newValue <= 10) {
        seatsInput.value = newValue;
    }
}

$(document).ready(function() {
    let selectedTripId = null;

    // تحميل المدن عند تغيير نوع الرحلة
    $('#tripType').change(function() {
        const tripType = $(this).val();
        if (tripType) {
            loadCities(tripType);
            $('#cityId').prop('disabled', false);
        } else {
            resetForm();
        }
    });

    // تحميل المحطات عند تغيير المدينة
    $('#cityId').change(function() {
        const cityId = $(this).val();
        const tripType = $('#tripType').val();
        if (cityId && tripType) {
            loadStations(cityId, tripType);
            $('#stationId').prop('disabled', false);
        } else {
            $('#stationId').prop('disabled', true).html('<option value="">اختر المحطة</option>');
        }
    });

    // تفعيل باقي الحقول عند اختيار المحطة
    $('#stationId').change(function() {
        if ($(this).val()) {
            $('#tripDate').prop('disabled', false);
            $('#seatsCount').prop('disabled', false);
            $('#searchTrips').prop('disabled', false);
            
            // تعيين الحد الأدنى للتاريخ (اليوم)
            const today = new Date().toISOString().split('T')[0];
            $('#tripDate').attr('min', today);
        }
    });

    // البحث عن الرحلات
    $('#searchTrips').click(function() {
        searchTrips();
    });

    // تأكيد الحجز
    $('#bookingForm').submit(function(e) {
        e.preventDefault();
        confirmBooking();
    });

    function loadCities(tripType) {
        $.ajax({
            url: 'bus-booking-api.php',
            method: 'POST',
            data: { action: 'get_cities', trip_type: tripType },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    let options = '<option value="">اختر المدينة</option>';
                    response.cities.forEach(function(city) {
                        options += `<option value="${city.id}">${city.name}</option>`;
                    });
                    $('#cityId').html(options);
                }
            }
        });
    }

    function loadStations(cityId, tripType) {
        $.ajax({
            url: 'bus-booking-api.php',
            method: 'POST',
            data: { action: 'get_stations', city_id: cityId, trip_type: tripType },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    let options = '<option value="">اختر المحطة</option>';
                    response.stations.forEach(function(station) {
                        options += `<option value="${station.id}">${station.name}</option>`;
                    });
                    $('#stationId').html(options);
                }
            }
        });
    }

    function searchTrips() {
        const formData = {
            action: 'search_trips',
            trip_type: $('#tripType').val(),
            station_id: $('#stationId').val(),
            trip_date: $('#tripDate').val(),
            seats_count: $('#seatsCount').val()
        };

        $('#tripsList').html('<div class="loading">جاري البحث...</div>');
        $('#tripsContainer').removeClass('hidden');

        $.ajax({
            url: 'bus-booking-api.php',
            method: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success && response.trips.length > 0) {
                    displayTrips(response.trips);
                } else {
                    $('#tripsList').html('<div class="alert alert-danger">لا توجد رحلات متاحة في هذا التاريخ</div>');
                }
            },
            error: function() {
                $('#tripsList').html('<div class="alert alert-danger">حدث خطأ أثناء البحث</div>');
            }
        });
    }

    function displayTrips(trips) {
        let html = '';
        trips.forEach(function(trip) {
            html += `
                <div class="trip-card" data-trip-id="${trip.id}">
                    <div class="trip-info">
                        <div>
                            <div class="trip-time">${trip.departure_time} - ${trip.expected_arrival_time}</div>
                            <div>من: ${trip.departure_station} إلى: ${trip.arrival_station}</div>
                        </div>
                        <div class="trip-price">${trip.seat_price} ريال</div>
                    </div>
                </div>
            `;
        });
        $('#tripsList').html(html);

        // إضافة حدث النقر على الرحلة
        $('.trip-card').click(function() {
            $('.trip-card').removeClass('selected');
            $(this).addClass('selected');
            selectedTripId = $(this).data('trip-id');
            
            // إظهار نموذج بيانات العميل
            $('#selectedTripId').val(selectedTripId);
            $('#selectedTripDate').val($('#tripDate').val());
            $('#selectedSeatsCount').val($('#seatsCount').val());
            $('#customerForm').removeClass('hidden');
        });
    }

    function confirmBooking() {
        const formData = $('#bookingForm').serialize() + '&action=confirm_booking';
        
        $.ajax({
            url: 'bus-booking-api.php',
            method: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // إعادة التوجيه إلى صفحة التأكيد
                    if (response.redirect_url) {
                        window.location.href = response.redirect_url;
                    } else {
                        $('#messages').html(`
                            <div class="alert alert-success">
                                <h4>تم تأكيد الحجز بنجاح! 🎉</h4>
                                <p><strong>رقم الحجز:</strong> ${response.booking_id}</p>
                                <p><strong>اسم العميل:</strong> ${response.customer_name}</p>
                                <p><strong>عدد المقاعد:</strong> ${response.seats_count}</p>
                                <p><strong>تاريخ الرحلة:</strong> ${response.trip_date}</p>
                            </div>
                        `);

                        // إعادة تعيين النموذج
                        resetForm();
                        $('#customerForm').addClass('hidden');
                        $('#tripsContainer').addClass('hidden');
                    }
                } else {
                    $('#messages').html(`<div class="alert alert-danger">خطأ: ${response.message}</div>`);
                }
            },
            error: function() {
                $('#messages').html('<div class="alert alert-danger">حدث خطأ أثناء تأكيد الحجز</div>');
            }
        });
    }

    function resetForm() {
        $('#cityId').prop('disabled', true).html('<option value="">اختر المدينة</option>');
        $('#stationId').prop('disabled', true).html('<option value="">اختر المحطة</option>');
        $('#tripDate').prop('disabled', true).val('');
        $('#seatsCount').prop('disabled', true).val('');
        $('#searchTrips').prop('disabled', true);
        $('#tripsContainer').addClass('hidden');
        $('#customerForm').addClass('hidden');
        selectedTripId = null;
    }
});
</script>

<?php require_once 'footer.php'; ?>
