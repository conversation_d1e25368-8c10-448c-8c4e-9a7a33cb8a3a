-- ========================================
-- نظام حجز مقاعد الباصات - إعداد قاعدة البيانات
-- ========================================

-- إنشاء الجداول إذا لم تكن موجودة
CREATE TABLE IF NOT EXISTS `bus_cities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `bus_stations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `city_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  <PERSON><PERSON>Y `fk_station_city` (`city_id`),
  CONSTRAINT `fk_station_city` FOREIGN KEY (`city_id`) REFERENCES `bus_cities` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `bus_trips` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `trip_type` enum('to_makkah','from_makkah') NOT NULL,
  `departure_station_id` int(11) NOT NULL,
  `arrival_station_id` int(11) NOT NULL,
  `departure_time` time NOT NULL,
  `expected_arrival_time` time NOT NULL,
  `seat_price` decimal(8,2) NOT NULL,
  `available_days` text NOT NULL COMMENT 'JSON array or "0" for daily',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  KEY `fk_trip_departure_station` (`departure_station_id`),
  KEY `fk_trip_arrival_station` (`arrival_station_id`),
  CONSTRAINT `fk_trip_departure_station` FOREIGN KEY (`departure_station_id`) REFERENCES `bus_stations` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_trip_arrival_station` FOREIGN KEY (`arrival_station_id`) REFERENCES `bus_stations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `bus_bookings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `trip_id` int(11) NOT NULL,
  `trip_date` date NOT NULL,
  `customer_name` varchar(255) NOT NULL,
  `customer_phone` varchar(20) NOT NULL,
  `seats_requested` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `fk_booking_trip` (`trip_id`),
  CONSTRAINT `fk_booking_trip` FOREIGN KEY (`trip_id`) REFERENCES `bus_trips` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ========================================
-- إضافة البيانات التجريبية
-- ========================================

-- إضافة المدن
INSERT INTO `bus_cities` (`name`) VALUES 
('الرياض'),
('مكة المكرمة');

-- إضافة المحطات
INSERT INTO `bus_stations` (`city_id`, `name`) VALUES 
(1, 'محطة البطحاء'),
(2, 'شارع ابراهيم الخليل');

-- إضافة الرحلات
INSERT INTO `bus_trips` (`trip_type`, `departure_station_id`, `arrival_station_id`, `departure_time`, `expected_arrival_time`, `seat_price`, `available_days`, `is_active`) VALUES 
('to_makkah', 1, 2, '13:30:00', '19:00:00', 100.00, '0', 1),
('from_makkah', 2, 1, '08:00:00', '13:30:00', 100.00, '0', 1);

-- ========================================
-- تأكيد إنشاء الجداول
-- ========================================
SELECT 'تم إنشاء جداول نظام حجز الباصات بنجاح' AS status;
